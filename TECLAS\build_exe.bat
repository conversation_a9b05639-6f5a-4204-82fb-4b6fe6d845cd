@echo off
echo ========================================
echo    Auto E Presser - Compilador a EXE
echo ========================================
echo.

echo 📦 Instalando dependencias necesarias...
pip install pynput pyinstaller

echo.
echo 🔨 Compilando script a EXE...
echo    Esto puede tomar unos minutos...

pyinstaller --onefile --noconsole --name "AutoEPresser" auto_e_python.py

echo.
if exist "dist\AutoEPresser.exe" (
    echo ✅ ¡EXE creado exitosamente!
    echo 📁 Ubicación: dist\AutoEPresser.exe
    echo.
    echo 🎮 Instrucciones de uso:
    echo    - Ejecuta AutoEPresser.exe
    echo    - F1 = Iniciar/Pausar presionar 'e'
    echo    - F2 = Cerrar programa
    echo    - ESC = Pausa de emergencia
    echo.
    echo 💡 El archivo EXE está en la carpeta 'dist'
) else (
    echo ❌ Error: No se pudo crear el EXE
    echo 💡 Verifica que Python y pip estén instalados correctamente
)

echo.
pause
