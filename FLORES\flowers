/* Atardecer de <PERSON> - CSS */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  height: 100vh;
  overflow: hidden;
  font-family: 'Georgia', serif;
  position: relative;
}

/* Clase container para animación de entrada */
body.container {
  opacity: 0;
  transform: scale(0.8);
  transition: all 1.5s ease-in-out;
}

/* Estado final después de remover la clase container */
body:not(.container) {
  opacity: 1;
  transform: scale(1);
}

/* Fondo de atardecer */
.sunset {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    180deg,
    #FF6B6B 0%,
    #FFE66D 25%,
    #FF8E53 50%,
    #FF6B9D 75%,
    #C44569 100%
  );
  z-index: -1;
}

/* Sol del atardecer */
.sunset::before {
  content: '';
  position: absolute;
  top: 10%;
  right: 15%;
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, #FFE135, #FF8C42);
  border-radius: 50%;
  box-shadow: 0 0 50px rgba(255, 140, 66, 0.8);
  animation: sunGlow 4s ease-in-out infinite;
}

@keyframes sunGlow {
  0%, 100% {
    box-shadow: 0 0 50px rgba(255, 140, 66, 0.8);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 80px rgba(255, 140, 66, 1);
    transform: scale(1.1);
  }
}

/* Contenedor de flores */
.flowers {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Flores adicionales */
.flowers--extra {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Estilos base de flores */
.flower {
  position: absolute;
  bottom: 0;
  animation: flowerSway 4s ease-in-out infinite;
}

.flower--1 {
  left: 20%;
  animation-delay: -0.5s;
}

.flower--2 {
  left: 40%;
  animation-delay: -1s;
}

.flower--3 {
  left: 60%;
  animation-delay: -1.5s;
}

.flower--4 {
  left: 10%;
  animation-delay: -2s;
}

.flower--5 {
  left: 75%;
  animation-delay: -2.5s;
}

.flower--6 {
  left: 85%;
  animation-delay: -3s;
}

@keyframes flowerSway {
  0%, 100% { transform: rotate(-2deg); }
  50% { transform: rotate(2deg); }
}

/* Cabeza de la flor */
.flower__leafs {
  position: relative;
  width: 60px;
  height: 60px;
  margin: 0 auto;
  animation: bloom 3s ease-out forwards;
  opacity: 0;
}

.flower__leafs--1 { animation-delay: 1s; }
.flower__leafs--2 { animation-delay: 1.5s; }
.flower__leafs--3 { animation-delay: 2s; }
.flower__leafs--4 { animation-delay: 2.5s; }
.flower__leafs--5 { animation-delay: 3s; }
.flower__leafs--6 { animation-delay: 3.5s; }

@keyframes bloom {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(360deg);
  }
}

/* Pétalos de las flores */
.flower__leaf {
  position: absolute;
  width: 25px;
  height: 35px;
  background: radial-gradient(ellipse, #FFD700, #FFA500);
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  transform-origin: center bottom;
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.7);
}

.flower__leaf--1 {
  transform: rotate(0deg) translateY(-15px);
  top: 50%; left: 50%;
  margin-left: -12.5px; margin-top: -17.5px;
}

.flower__leaf--2 {
  transform: rotate(90deg) translateY(-15px);
  top: 50%; left: 50%;
  margin-left: -12.5px; margin-top: -17.5px;
}

.flower__leaf--3 {
  transform: rotate(180deg) translateY(-15px);
  top: 50%; left: 50%;
  margin-left: -12.5px; margin-top: -17.5px;
}

.flower__leaf--4 {
  transform: rotate(270deg) translateY(-15px);
  top: 50%; left: 50%;
  margin-left: -12.5px; margin-top: -17.5px;
}

/* Centro de la flor */
.flower__white-circle {
  position: absolute;
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, #8B4513, #D2691E);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

/* Luces de la flor */
.flower__light {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #FFD700;
  border-radius: 50%;
  animation: sparkle 2s ease-in-out infinite;
}

.flower__light--1 { top: 10%; left: 20%; animation-delay: -0.2s; }
.flower__light--2 { top: 20%; right: 15%; animation-delay: -0.4s; }
.flower__light--3 { bottom: 15%; left: 25%; animation-delay: -0.6s; }
.flower__light--4 { bottom: 20%; right: 20%; animation-delay: -0.8s; }
.flower__light--5 { top: 30%; left: 10%; animation-delay: -1s; }
.flower__light--6 { top: 40%; right: 10%; animation-delay: -1.2s; }
.flower__light--7 { bottom: 30%; left: 15%; animation-delay: -1.4s; }
.flower__light--8 { bottom: 35%; right: 25%; animation-delay: -1.6s; }

@keyframes sparkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.5);
  }
}

/* Tallo de la flor */
.flower__line {
  width: 6px;
  height: 120px;
  background: linear-gradient(to top, #2d5016, #4a7c59);
  border-radius: 3px;
  margin: 0 auto;
  position: relative;
  animation: stemGrow 2s ease-out forwards;
  transform-origin: bottom;
  transform: scaleY(0);
}

@keyframes stemGrow {
  0% { transform: scaleY(0); }
  100% { transform: scaleY(1); }
}

/* Hojas del tallo */
.flower__line__leaf {
  position: absolute;
  width: 15px;
  height: 25px;
  background: linear-gradient(45deg, #228B22, #32CD32);
  border-radius: 0 100% 0 100%;
  animation: leafGrow 1s ease-out forwards;
  opacity: 0;
}

.flower__line__leaf--1 {
  top: 20%; left: -8px;
  transform: rotate(-30deg);
  animation-delay: 2s;
}

.flower__line__leaf--2 {
  top: 40%; right: -8px;
  transform: rotate(30deg);
  animation-delay: 2.2s;
}

.flower__line__leaf--3 {
  top: 60%; left: -8px;
  transform: rotate(-30deg);
  animation-delay: 2.4s;
}

.flower__line__leaf--4 {
  top: 80%; right: -8px;
  transform: rotate(30deg);
  animation-delay: 2.6s;
}

.flower__line__leaf--5 {
  top: 30%; left: -10px;
  transform: rotate(-45deg);
  animation-delay: 2.8s;
}

.flower__line__leaf--6 {
  top: 70%; right: -10px;
  transform: rotate(45deg);
  animation-delay: 3s;
}

@keyframes leafGrow {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Mensaje */
.message {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.9);
  color: #8B4513;
  padding: 20px 30px;
  border-radius: 25px;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  border: 3px solid #FFD700;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
  animation: messageFloat 3s ease-in-out infinite;
  z-index: 100;
}

@keyframes messageFloat {
  0%, 100% { transform: translateX(-50%) translateY(0px); }
  50% { transform: translateX(-50%) translateY(-10px); }
}

/* Elementos adicionales de hierba y plantas */
.grow-ans {
  animation: growUp 2s ease-out forwards;
  opacity: 0;
}

@keyframes growUp {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive para móviles */
@media screen and (max-width: 412px) {
  .sunset::before {
    width: 60px;
    height: 60px;
    top: 8%;
    right: 10%;
  }

  .flower__leafs {
    width: 50px;
    height: 50px;
  }

  .flower__leaf {
    width: 20px;
    height: 28px;
  }

  .message {
    font-size: 16px;
    padding: 15px 20px;
    bottom: 15px;
  }
}