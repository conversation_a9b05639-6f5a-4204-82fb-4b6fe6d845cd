; Script de AutoHotkey para presionar la tecla 'e' infinitamente
; Presiona F1 para iniciar/pausar el script
; Presiona F2 para detener completamente el script

#NoEnv
#SingleInstance Force
SendMode Input
SetWorkingDir %A_ScriptDir%

; Variables globales
isRunning := false
loopActive := false

; F1 para iniciar/pausar el bucle infinito de presionar 'e'
F1::
    if (!isRunning) {
        isRunning := true
        loopActive := true
        ToolTip, Presionando 'e' infinitamente... (F1 para pausar, F2 para detener)
        SetTimer, PressE, 100  ; Presiona 'e' cada 100 milisegundos
    } else {
        isRunning := false
        loopActive := false
        SetTimer, PressE, Off
        ToolTip, Pausado. Presiona F1 para reanudar o F2 para salir.
    }
return

; F2 para detener completamente el script
F2::
    isRunning := false
    loopActive := false
    SetTimer, PressE, Off
    ToolTip, Script detenido. Cerrando...
    Sleep, 1000
    ToolTip
    ExitApp
return

; Función que presiona la tecla 'e'
PressE:
    if (loopActive) {
        Send, e
    }
return

; Mostrar instrucciones al iniciar
ToolTip, Script cargado. F1 = Iniciar/Pausar presionar 'e', F2 = Salir
SetTimer, HideTooltip, 3000

HideTooltip:
    ToolTip
    SetTimer, HideTooltip, Off
return

; ESC como tecla de emergencia para detener todo
Esc::
    isRunning := false
    loopActive := false
    SetTimer, PressE, Off
    ToolTip, DETENIDO POR ESC - Script pausado
return
