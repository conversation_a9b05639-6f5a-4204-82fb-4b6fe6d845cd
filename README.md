# Auto E Presser - Versión Python

Script optimizado en Python para presionar la tecla 'e' infinitamente con bajo consumo de recursos.

## 🚀 Compilar a EXE (Método Rápido)

1. **Ejecuta el compilador automático:**
   ```
   build_exe.bat
   ```
   
2. **El EXE se creará en:** `dist/AutoEPresser.exe`

## 🛠️ Instalación Manual

Si prefieres hacerlo paso a paso:

1. **Instalar dependencias:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Ejecutar directamente:**
   ```bash
   python auto_e_python.py
   ```

3. **Compilar a EXE manualmente:**
   ```bash
   pyinstaller --onefile --noconsole --name "AutoEPresser" auto_e_python.py
   ```

## 🎮 Controles

- **F1** = Iniciar/Pausar presionar 'e'
- **F2** = Cerrar programa completamente  
- **ESC** = Pausa de emergencia

## ⚡ Optimizaciones de Rendimiento

- **Hilos separados:** El presionado de teclas no bloquea la interfaz
- **Sleep inteligente:** Reduce el uso de CPU al mínimo
- **Intervalos optimizados:** 100ms entre pulsaciones (10 por segundo)
- **Gestión de memoria:** Liberación automática de recursos

## 🔧 Características Técnicas

- **Lenguaje:** Python 3.7+
- **Dependencias:** pynput, pyinstaller
- **Consumo CPU:** < 1% en sistemas modernos
- **Memoria:** ~10-15 MB como EXE
- **Compatibilidad:** Windows 10/11

## 📁 Archivos Incluidos

- `auto_e_python.py` - Script principal
- `build_exe.bat` - Compilador automático
- `requirements.txt` - Dependencias
- `README.md` - Este archivo

## 🐛 Solución de Problemas

**Error: "pynput no está instalado"**
```bash
pip install pynput
```

**Error: "pyinstaller no encontrado"**
```bash
pip install pyinstaller
```

**El EXE no funciona:**
- Ejecuta como administrador
- Verifica que no esté bloqueado por antivirus
- Prueba primero el script Python directamente

## 💡 Notas

- El script funciona en segundo plano
- Muy bajo consumo de recursos
- Se puede minimizar a la bandeja del sistema
- Compatible con juegos y aplicaciones
