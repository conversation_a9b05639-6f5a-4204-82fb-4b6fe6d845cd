<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, maximum-scale=5.0, minimum-scale=0.5">
    <title><PERSON><PERSON> Estrellada con <PERSON> 🌻✨</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(180deg, #0a0a2e 0%, #16213e 30%, #1a1a3a 70%, #000000 100%);
            min-height: 100vh;
            overflow: hidden;
            font-family: 'Georgia', serif;
            position: relative;
            touch-action: pan-x pan-y;
        }

        .container {
            position: relative;
            width: 100%;
            height: 100vh;
            transform-origin: center center;
            transition: transform 0.3s ease;
        }

        /* Estrellas */
        .star {
            position: absolute;
            background: #ffffff;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
        }

        .star.small {
            width: 3px;
            height: 3px;
            animation: twinkle 2s ease-in-out infinite;
        }

        .star.medium {
            width: 5px;
            height: 5px;
            animation: twinkle 3s ease-in-out infinite;
        }

        .star.large {
            width: 8px;
            height: 8px;
            animation: twinkle 4s ease-in-out infinite;
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.9);
        }

        .star.interactive {
            width: 12px;
            height: 12px;
            animation: pulse 2s ease-in-out infinite;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
            background: radial-gradient(circle, #FFD700, #FFA500);
        }

        /* Mensaje de estrella */
        .star-message {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: #FFD700;
            padding: 15px 20px;
            border-radius: 15px;
            font-size: 14px;
            max-width: 250px;
            text-align: center;
            border: 2px solid #FFD700;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
            opacity: 0;
            transform: scale(0);
            transition: all 0.3s ease;
            z-index: 1000;
            pointer-events: none;
        }

        .star-message.show {
            opacity: 1;
            transform: scale(1);
        }

        /* Flores brotando */
        .flower {
            position: absolute;
            bottom: -100px;
            animation: grow 4s ease-out forwards;
        }

        .stem {
            width: 4px;
            height: 0;
            background: linear-gradient(to top, #2d5016, #4a7c59);
            border-radius: 2px;
            margin: 0 auto;
            animation: stemGrow 3s ease-out forwards;
        }

        .flower-head {
            width: 35px;
            height: 35px;
            position: relative;
            margin: 0 auto;
            opacity: 0;
            animation: bloom 2s ease-out 2s forwards;
        }

        .petal {
            position: absolute;
            width: 15px;
            height: 20px;
            background: radial-gradient(ellipse, #FFD700, #FFA500);
            border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
            transform-origin: center bottom;
            box-shadow: 0 0 8px rgba(255, 215, 0, 0.6);
        }

        .petal:nth-child(1) { transform: rotate(0deg) translateY(-10px); }
        .petal:nth-child(2) { transform: rotate(45deg) translateY(-10px); }
        .petal:nth-child(3) { transform: rotate(90deg) translateY(-10px); }
        .petal:nth-child(4) { transform: rotate(135deg) translateY(-10px); }
        .petal:nth-child(5) { transform: rotate(180deg) translateY(-10px); }
        .petal:nth-child(6) { transform: rotate(225deg) translateY(-10px); }
        .petal:nth-child(7) { transform: rotate(270deg) translateY(-10px); }
        .petal:nth-child(8) { transform: rotate(315deg) translateY(-10px); }

        .flower-center {
            position: absolute;
            width: 12px;
            height: 12px;
            background: radial-gradient(circle, #8B4513, #D2691E);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        /* Cometas */
        .comet {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ffffff;
            border-radius: 50%;
            box-shadow: 0 0 10px #ffffff;
        }

        .comet::after {
            content: '';
            position: absolute;
            width: 100px;
            height: 2px;
            background: linear-gradient(90deg, rgba(255,255,255,0.8), transparent);
            top: 1px;
            left: -100px;
            border-radius: 1px;
        }

        /* Luna */
        .moon {
            position: absolute;
            top: 80px;
            right: 60px;
            width: 60px;
            height: 60px;
            background: radial-gradient(circle at 30% 30%, #FFF8DC, #F0E68C);
            border-radius: 50%;
            box-shadow: 0 0 30px rgba(255, 248, 220, 0.6);
            animation: moonGlow 6s ease-in-out infinite;
        }

        /* Instrucciones */
        .instructions {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: #FFD700;
            padding: 15px;
            border-radius: 10px;
            font-size: 12px;
            z-index: 1001;
            border: 1px solid #FFD700;
            max-width: 280px;
        }

        /* Animaciones */
        @keyframes twinkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.7; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.3); }
        }

        @keyframes grow {
            from { bottom: -100px; }
            to { bottom: 0px; }
        }

        @keyframes stemGrow {
            from { height: 0; }
            to { height: 80px; }
        }

        @keyframes bloom {
            from { opacity: 0; transform: scale(0) rotate(0deg); }
            to { opacity: 1; transform: scale(1) rotate(360deg); }
        }

        @keyframes cometFly {
            from { 
                top: -10px; 
                left: 100%; 
                opacity: 1; 
            }
            to { 
                top: 100%; 
                left: -150px; 
                opacity: 0; 
            }
        }

        @keyframes moonGlow {
            0%, 100% { box-shadow: 0 0 30px rgba(255, 248, 220, 0.6); }
            50% { box-shadow: 0 0 50px rgba(255, 248, 220, 0.9); }
        }

        /* Responsive para Samsung A51 */
        @media screen and (max-width: 412px) {
            .instructions {
                font-size: 11px;
                padding: 12px;
                max-width: 250px;
            }
            
            .star-message {
                font-size: 12px;
                max-width: 200px;
                padding: 12px 16px;
            }
            
            .moon {
                width: 50px;
                height: 50px;
                top: 60px;
                right: 40px;
            }
        }
    </style>
</head>
<body>
    <div class="instructions">
        🌟 Toca las estrellas doradas para ver mensajes bonitos<br>
        🔍 Usa zoom con los dedos para acercarte<br>
        🌻 Observa las flores brotar del suelo
    </div>

    <div class="container" id="container">
        <!-- Luna -->
        <div class="moon"></div>

        <!-- Estrellas interactivas con mensajes -->
        <div class="star interactive" style="top: 120px; left: 80px;" data-message="✨ Eres una estrella brillante en la oscuridad ✨"></div>
        <div class="star interactive" style="top: 200px; right: 100px;" data-message="🌟 Tus sueños son tan infinitos como el universo 🌟"></div>
        <div class="star interactive" style="top: 300px; left: 150px;" data-message="💫 Cada noche es una nueva oportunidad para brillar 💫"></div>
        <div class="star interactive" style="top: 180px; left: 250px;" data-message="⭐ Tu luz interior ilumina el camino de otros ⭐"></div>
        <div class="star interactive" style="top: 400px; right: 80px;" data-message="🌠 Eres única e irrepetible como cada estrella 🌠"></div>
        <div class="star interactive" style="top: 350px; left: 50px;" data-message="✨ La magia está en creer en ti misma ✨"></div>

        <!-- Estrellas decorativas -->
        <div class="star small" style="top: 50px; left: 30px; animation-delay: -0.5s;"></div>
        <div class="star medium" style="top: 80px; right: 200px; animation-delay: -1s;"></div>
        <div class="star small" style="top: 150px; left: 200px; animation-delay: -1.5s;"></div>
        <div class="star large" style="top: 250px; right: 150px; animation-delay: -2s;"></div>
        <div class="star small" style="top: 320px; left: 300px; animation-delay: -0.8s;"></div>
        <div class="star medium" style="top: 450px; right: 200px; animation-delay: -1.2s;"></div>
        <div class="star small" style="top: 100px; left: 350px; animation-delay: -2.5s;"></div>
        <div class="star large" style="top: 380px; left: 200px; animation-delay: -0.3s;"></div>

        <!-- Mensaje flotante para estrellas -->
        <div class="star-message" id="starMessage"></div>
    </div>

    <script>
        // Variables globales
        let scale = 1;
        let isDragging = false;
        let startDistance = 0;
        let container = document.getElementById('container');
        let starMessage = document.getElementById('starMessage');
        let messageTimeout;

        // Función para crear flores que brotan
        function createFlower(delay = 0) {
            setTimeout(() => {
                const flower = document.createElement('div');
                flower.className = 'flower';
                flower.style.left = Math.random() * (window.innerWidth - 50) + 'px';
                flower.style.animationDelay = Math.random() * 2 + 's';
                
                flower.innerHTML = `
                    <div class="flower-head">
                        <div class="petal"></div>
                        <div class="petal"></div>
                        <div class="petal"></div>
                        <div class="petal"></div>
                        <div class="petal"></div>
                        <div class="petal"></div>
                        <div class="petal"></div>
                        <div class="petal"></div>
                        <div class="flower-center"></div>
                    </div>
                    <div class="stem"></div>
                `;
                
                container.appendChild(flower);
                
                // Remover la flor después de la animación
                setTimeout(() => {
                    if (flower.parentNode) {
                        flower.parentNode.removeChild(flower);
                    }
                }, 8000);
            }, delay);
        }

        // Función para crear cometas
        function createComet() {
            const comet = document.createElement('div');
            comet.className = 'comet';
            comet.style.top = Math.random() * 200 + 'px';
            comet.style.left = '100%';
            comet.style.animation = 'cometFly 3s linear forwards';
            
            container.appendChild(comet);
            
            setTimeout(() => {
                if (comet.parentNode) {
                    comet.parentNode.removeChild(comet);
                }
            }, 3000);
        }

        // Manejo de zoom con gestos táctiles
        function getDistance(touches) {
            const dx = touches[0].clientX - touches[1].clientX;
            const dy = touches[0].clientY - touches[1].clientY;
            return Math.sqrt(dx * dx + dy * dy);
        }

        // Eventos táctiles para zoom
        container.addEventListener('touchstart', function(e) {
            if (e.touches.length === 2) {
                isDragging = true;
                startDistance = getDistance(e.touches);
                e.preventDefault();
            }
        });

        container.addEventListener('touchmove', function(e) {
            if (isDragging && e.touches.length === 2) {
                const currentDistance = getDistance(e.touches);
                const scaleChange = currentDistance / startDistance;
                const newScale = scale * scaleChange;
                
                if (newScale >= 0.5 && newScale <= 5) {
                    container.style.transform = `scale(${newScale})`;
                }
                e.preventDefault();
            }
        });

        container.addEventListener('touchend', function(e) {
            if (isDragging) {
                const currentScale = parseFloat(container.style.transform.replace('scale(', '').replace(')', '')) || 1;
                scale = currentScale;
                isDragging = false;
            }
        });

        // Manejo de clics en estrellas interactivas
        document.querySelectorAll('.star.interactive').forEach(star => {
            star.addEventListener('click', function(e) {
                const message = this.getAttribute('data-message');
                const rect = this.getBoundingClientRect();
                
                starMessage.textContent = message;
                starMessage.style.left = (rect.left - 100) + 'px';
                starMessage.style.top = (rect.top - 80) + 'px';
                starMessage.classList.add('show');
                
                // Ocultar mensaje después de 3 segundos
                clearTimeout(messageTimeout);
                messageTimeout = setTimeout(() => {
                    starMessage.classList.remove('show');
                }, 3000);
                
                e.stopPropagation();
            });
        });

        // Ocultar mensaje al tocar fuera
        document.addEventListener('click', function() {
            starMessage.classList.remove('show');
        });

        // Inicializar flores y cometas
        function init() {
            // Crear flores iniciales
            for (let i = 0; i < 5; i++) {
                createFlower(i * 1000);
            }
            
            // Crear flores periódicamente
            setInterval(() => {
                createFlower();
            }, 3000);
            
            // Crear cometas ocasionalmente
            setInterval(() => {
                if (Math.random() < 0.3) {
                    createComet();
                }
            }, 5000);
        }

        // Iniciar cuando la página cargue
        window.addEventListener('load', init);
    </script>
</body>
</html>
