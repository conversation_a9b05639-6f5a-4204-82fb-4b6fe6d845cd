const garden = document.querySelector(".garden");

// Tipos de flores, todas amarillas
const flowerTypes = {
  sunflower: `
    <svg viewBox="0 0 100 100">
      <defs>
        <radialGradient id="petalSun" cx="50%" cy="50%" r="50%">
          <stop offset="0%" stop-color="#fff176" />
          <stop offset="100%" stop-color="#fbc02d" />
        </radialGradient>
        <radialGradient id="centerSun" cx="50%" cy="50%" r="50%">
          <stop offset="0%" stop-color="#ffeb3b"/>
          <stop offset="100%" stop-color="#f57f17"/>
        </radialGradient>
      </defs>
      <g>
        ${Array(16).fill(`<ellipse cx="50" cy="20" rx="10" ry="30" fill="url(#petalSun)" transform="rotate(__deg,50,50)"/>`)
        .map((el,i)=>el.replace("__",i*22.5)).join("")}
        <circle cx="50" cy="50" r="18" fill="url(#centerSun)" />
      </g>
    </svg>
  `,
  marigold: `
    <svg viewBox="0 0 100 100">
      <defs>
        <radialGradient id="petalMari" cx="50%" cy="50%" r="50%">
          <stop offset="0%" stop-color="#ffe082" />
          <stop offset="100%" stop-color="#ffb300" />
        </radialGradient>
        <radialGradient id="centerMari" cx="50%" cy="50%" r="50%">
          <stop offset="0%" stop-color="#fff176"/>
          <stop offset="100%" stop-color="#f57f17"/>
        </radialGradient>
      </defs>
      <g>
        ${Array(20).fill(`<ellipse cx="50" cy="15" rx="8" ry="20" fill="url(#petalMari)" transform="rotate(__deg,50,50)"/>`)
        .map((el,i)=>el.replace("__",i*18)).join("")}
        <circle cx="50" cy="50" r="15" fill="url(#centerMari)" />
      </g>
    </svg>
  `,
  daisyYellow: `
    <svg viewBox="0 0 100 100">
      <defs>
        <radialGradient id="petalDaisyY" cx="50%" cy="50%" r="50%">
          <stop offset="0%" stop-color="#fff9c4" />
          <stop offset="100%" stop-color="#fdd835" />
        </radialGradient>
        <radialGradient id="centerDaisyY" cx="50%" cy="50%" r="50%">
          <stop offset="0%" stop-color="#ffee58"/>
          <stop offset="100%" stop-color="#fbc02d"/>
        </radialGradient>
      </defs>
      <g>
        ${Array(12).fill(`<ellipse cx="50" cy="20" rx="12" ry="28" fill="url(#petalDaisyY)" transform="rotate(__deg,50,50)"/>`)
        .map((el,i)=>el.replace("__",i*30)).join("")}
        <circle cx="50" cy="50" r="12" fill="url(#centerDaisyY)" />
      </g>
    </svg>
  `
};

function createFlower(x, delay, type) {
  const flower = document.createElement("div");
  flower.classList.add("flower");
  flower.style.left = `${x}%`;
  flower.style.animationDelay = `${delay}s`;

  const stem = document.createElement("div");
  stem.classList.add("stem");
  flower.appendChild(stem);

  flower.innerHTML += flowerTypes[type];
  garden.appendChild(flower);
}

// Muchas flores más juntas
const types = Object.keys(flowerTypes);
for (let i = 0; i < 35; i++) {
  const type = types[Math.floor(Math.random() * types.length)];
  createFlower(Math.random() * 95, Math.random() * 3, type);
}
