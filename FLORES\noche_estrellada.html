<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, maximum-scale=5.0, minimum-scale=0.5">
    <title><PERSON><PERSON> Estrellada con Flores 🌻✨</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(180deg, #FF6B6B 0%, #FFE66D 25%, #FF8E53 50%, #FF6B9D 75%, #C44569 100%);
            min-height: 100vh;
            overflow: hidden;
            font-family: 'Georgia', serif;
            position: relative;
            touch-action: pan-x pan-y;
        }

        .container {
            position: relative;
            width: 100%;
            height: 100vh;
            transform-origin: center center;
            transition: transform 0.3s ease;
        }

        /* Estrellas */
        .star {
            position: absolute;
            background: #ffffff;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
        }

        .star.small {
            width: 3px;
            height: 3px;
            animation: twinkle 2s ease-in-out infinite;
        }

        .star.medium {
            width: 5px;
            height: 5px;
            animation: twinkle 3s ease-in-out infinite;
        }

        .star.large {
            width: 8px;
            height: 8px;
            animation: twinkle 4s ease-in-out infinite;
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.9);
        }

        .star.interactive {
            width: 12px;
            height: 12px;
            animation: pulse 2s ease-in-out infinite;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
            background: radial-gradient(circle, #FFD700, #FFA500);
        }

        /* Mensaje de estrella */
        .star-message {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: #FFD700;
            padding: 15px 20px;
            border-radius: 15px;
            font-size: 14px;
            max-width: 250px;
            text-align: center;
            border: 2px solid #FFD700;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
            opacity: 0;
            transform: scale(0);
            transition: all 0.3s ease;
            z-index: 1000;
            pointer-events: none;
        }

        .star-message.show {
            opacity: 1;
            transform: scale(1);
        }

        /* Flores estáticas */
        .flower {
            position: absolute;
            cursor: pointer;
            transition: all 0.3s ease;
            animation: gentleSway 4s ease-in-out infinite;
        }

        .flower.small {
            transform: scale(0.6);
        }

        .flower.medium {
            transform: scale(0.8);
        }

        .flower.large {
            transform: scale(1.2);
        }

        .flower.extra-large {
            transform: scale(1.5);
        }

        .stem {
            width: 6px;
            height: 60px;
            background: linear-gradient(to top, #2d5016, #4a7c59);
            border-radius: 3px;
            margin: 0 auto;
            position: relative;
        }

        .flower-head {
            width: 40px;
            height: 40px;
            position: relative;
            margin: 0 auto;
            margin-bottom: 5px;
        }

        .petal {
            position: absolute;
            width: 18px;
            height: 25px;
            background: radial-gradient(ellipse, #FFD700, #FFA500);
            border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
            transform-origin: center bottom;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.7);
        }

        .petal:nth-child(1) { transform: rotate(0deg) translateY(-12px); }
        .petal:nth-child(2) { transform: rotate(45deg) translateY(-12px); }
        .petal:nth-child(3) { transform: rotate(90deg) translateY(-12px); }
        .petal:nth-child(4) { transform: rotate(135deg) translateY(-12px); }
        .petal:nth-child(5) { transform: rotate(180deg) translateY(-12px); }
        .petal:nth-child(6) { transform: rotate(225deg) translateY(-12px); }
        .petal:nth-child(7) { transform: rotate(270deg) translateY(-12px); }
        .petal:nth-child(8) { transform: rotate(315deg) translateY(-12px); }

        .flower-center {
            position: absolute;
            width: 15px;
            height: 15px;
            background: radial-gradient(circle, #8B4513, #D2691E);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        /* Mensaje de flor */
        .flower-message {
            position: absolute;
            background: rgba(255, 255, 255, 0.95);
            color: #8B4513;
            padding: 15px 20px;
            border-radius: 20px;
            font-size: 14px;
            max-width: 250px;
            text-align: center;
            border: 3px solid #FFD700;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
            opacity: 0;
            transform: scale(0);
            transition: all 0.3s ease;
            z-index: 1000;
            pointer-events: none;
            font-weight: bold;
        }

        .flower-message.show {
            opacity: 1;
            transform: scale(1);
        }

        /* Cometas */
        .comet {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ffffff;
            border-radius: 50%;
            box-shadow: 0 0 10px #ffffff;
        }

        .comet::after {
            content: '';
            position: absolute;
            width: 100px;
            height: 2px;
            background: linear-gradient(90deg, rgba(255,255,255,0.8), transparent);
            top: 1px;
            left: -100px;
            border-radius: 1px;
        }

        /* Sol del atardecer */
        .sun {
            position: absolute;
            top: 60px;
            right: 80px;
            width: 80px;
            height: 80px;
            background: radial-gradient(circle at 30% 30%, #FFE135, #FF8C42);
            border-radius: 50%;
            box-shadow: 0 0 40px rgba(255, 140, 66, 0.8);
            animation: sunGlow 4s ease-in-out infinite;
        }

        /* Instrucciones */
        .instructions {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            color: #8B4513;
            padding: 15px;
            border-radius: 15px;
            font-size: 12px;
            z-index: 1001;
            border: 2px solid #FFD700;
            max-width: 280px;
            font-weight: bold;
        }

        /* Animaciones */
        @keyframes twinkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.7; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.3); }
        }

        @keyframes gentleSway {
            0%, 100% { transform: rotate(-1deg) scale(var(--scale, 1)); }
            50% { transform: rotate(1deg) scale(var(--scale, 1)); }
        }

        @keyframes sunGlow {
            0%, 100% { box-shadow: 0 0 40px rgba(255, 140, 66, 0.8); }
            50% { box-shadow: 0 0 60px rgba(255, 140, 66, 1); }
        }

        @keyframes cometFly {
            from { 
                top: -10px; 
                left: 100%; 
                opacity: 1; 
            }
            to { 
                top: 100%; 
                left: -150px; 
                opacity: 0; 
            }
        }



        /* Responsive para Samsung A51 */
        @media screen and (max-width: 412px) {
            .instructions {
                font-size: 11px;
                padding: 12px;
                max-width: 250px;
            }
            
            .star-message {
                font-size: 12px;
                max-width: 200px;
                padding: 12px 16px;
            }
            
            .sun {
                width: 60px;
                height: 60px;
                top: 40px;
                right: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="instructions">
        � Toca las flores para ver mensajes bonitos<br>
        🔍 Usa zoom con los dedos para acercarte<br>
        � Disfruta del hermoso atardecer
    </div>

    <div class="container" id="container">
        <!-- Sol del atardecer -->
        <div class="sun"></div>

        <!-- Mensaje flotante para flores -->
        <div class="flower-message" id="flowerMessage"></div>
    </div>

    <script>
        // Variables globales
        let scale = 1;
        let isDragging = false;
        let startDistance = 0;
        let container = document.getElementById('container');
        let flowerMessage = document.getElementById('flowerMessage');
        let messageTimeout;

        // Mensajes bonitos para las flores
        const flowerMessages = [
            "🌻 Eres tan hermosa como esta flor dorada",
            "🌼 Tu sonrisa ilumina el mundo entero",
            "🌸 Cada día contigo es primavera",
            "🌺 Eres única y especial en el universo",
            "🌷 Tu corazón es puro como el oro",
            "🌹 Irradias belleza por donde caminas",
            "🌻 Eres mi rayo de sol en días grises",
            "🌼 Tu alma florece con cada acto de bondad",
            "🌸 Eres más valiosa que todas las flores",
            "🌺 Tu presencia hace todo más hermoso",
            "🌷 Eres un regalo precioso para el mundo",
            "🌹 Tu luz interior nunca se apaga",
            "🌻 Cada pétalo representa tu dulzura",
            "🌼 Eres la flor más bella del jardín",
            "🌸 Tu amor hace florecer corazones",
            "🌺 Eres perfecta tal como eres",
            "🌷 Tu esencia perfuma el aire",
            "🌹 Eres una obra maestra de la naturaleza",
            "🌻 Tu energía positiva contagia alegría",
            "🌼 Eres tan delicada como fuerte"
        ];

        // Función para crear una flor
        function createFlower(x, y, size, message) {
            const flower = document.createElement('div');
            flower.className = `flower ${size}`;
            flower.style.left = x + 'px';
            flower.style.bottom = y + 'px';
            flower.style.animationDelay = Math.random() * 4 + 's';
            flower.setAttribute('data-message', message);

            flower.innerHTML = `
                <div class="flower-head">
                    <div class="petal"></div>
                    <div class="petal"></div>
                    <div class="petal"></div>
                    <div class="petal"></div>
                    <div class="petal"></div>
                    <div class="petal"></div>
                    <div class="petal"></div>
                    <div class="petal"></div>
                    <div class="flower-center"></div>
                </div>
                <div class="stem"></div>
            `;

            // Agregar evento de clic
            flower.addEventListener('click', function(e) {
                showFlowerMessage(this, e);
            });

            container.appendChild(flower);
        }

        // Función para mostrar mensaje de flor
        function showFlowerMessage(flower, event) {
            const message = flower.getAttribute('data-message');
            const rect = flower.getBoundingClientRect();

            flowerMessage.textContent = message;
            flowerMessage.style.left = Math.max(10, rect.left - 100) + 'px';
            flowerMessage.style.top = Math.max(10, rect.top - 80) + 'px';
            flowerMessage.classList.add('show');

            // Ocultar mensaje después de 4 segundos
            clearTimeout(messageTimeout);
            messageTimeout = setTimeout(() => {
                flowerMessage.classList.remove('show');
            }, 4000);

            event.stopPropagation();
        }

        // Función para crear todas las flores
        function createAllFlowers() {
            const screenWidth = window.innerWidth;
            const screenHeight = window.innerHeight;
            const sizes = ['small', 'medium', 'large', 'extra-large'];

            // Crear flores en filas organizadas
            const rows = 8;
            const flowersPerRow = 6;

            for (let row = 0; row < rows; row++) {
                for (let col = 0; col < flowersPerRow; col++) {
                    const x = (col * (screenWidth / flowersPerRow)) + Math.random() * 60 - 30;
                    const y = (row * (screenHeight / rows)) + Math.random() * 40 - 20;
                    const size = sizes[Math.floor(Math.random() * sizes.length)];
                    const message = flowerMessages[Math.floor(Math.random() * flowerMessages.length)];

                    createFlower(x, y, size, message);
                }
            }

            // Crear flores adicionales aleatorias para llenar espacios
            for (let i = 0; i < 20; i++) {
                const x = Math.random() * (screenWidth - 100);
                const y = Math.random() * (screenHeight - 100);
                const size = sizes[Math.floor(Math.random() * sizes.length)];
                const message = flowerMessages[Math.floor(Math.random() * flowerMessages.length)];

                createFlower(x, y, size, message);
            }
        }

        // Manejo de zoom con gestos táctiles
        function getDistance(touches) {
            const dx = touches[0].clientX - touches[1].clientX;
            const dy = touches[0].clientY - touches[1].clientY;
            return Math.sqrt(dx * dx + dy * dy);
        }

        // Eventos táctiles para zoom
        container.addEventListener('touchstart', function(e) {
            if (e.touches.length === 2) {
                isDragging = true;
                startDistance = getDistance(e.touches);
                e.preventDefault();
            }
        });

        container.addEventListener('touchmove', function(e) {
            if (isDragging && e.touches.length === 2) {
                const currentDistance = getDistance(e.touches);
                const scaleChange = currentDistance / startDistance;
                const newScale = scale * scaleChange;

                if (newScale >= 0.5 && newScale <= 3) {
                    container.style.transform = `scale(${newScale})`;
                }
                e.preventDefault();
            }
        });

        container.addEventListener('touchend', function(e) {
            if (isDragging) {
                const currentScale = parseFloat(container.style.transform.replace('scale(', '').replace(')', '')) || 1;
                scale = currentScale;
                isDragging = false;
            }
        });

        // Ocultar mensaje al tocar fuera
        document.addEventListener('click', function() {
            flowerMessage.classList.remove('show');
        });

        // Inicializar
        function init() {
            createAllFlowers();
        }

        // Iniciar cuando la página cargue
        window.addEventListener('load', init);

        // Recrear flores si se redimensiona la pantalla
        window.addEventListener('resize', function() {
            // Limpiar flores existentes
            const existingFlowers = container.querySelectorAll('.flower');
            existingFlowers.forEach(flower => flower.remove());

            // Crear nuevas flores
            setTimeout(createAllFlowers, 100);
        });
    </script>
</body>
</html>
