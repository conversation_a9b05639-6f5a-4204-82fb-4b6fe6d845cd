; Script de AutoHotkey v2 para presionar la tecla 'e' infinitamente
; Presiona F1 para iniciar/pausar el script
; Presiona F2 para detener completamente el script

#Requires AutoHotkey v2.0
#SingleInstance Force
SendMode("Input")
SetWorkingDir(A_ScriptDir)

; Variables globales
isRunning := false
loopActive := false

; F1 para iniciar/pausar el bucle infinito de presionar 'e'
F1:: {
    global
    if (!isRunning) {
        isRunning := true
        loopActive := true
        ToolTip("Presionando 'e' infinitamente... (F1 para pausar, F2 para detener)")
        SetTimer(PressE, 100)  ; Presiona 'e' cada 100 milisegundos
    } else {
        isRunning := false
        loopActive := false
        SetTimer(PressE, 0)
        ToolTip("Pausado. Presiona F1 para reanudar o F2 para salir.")
    }
}

; F2 para detener completamente el script
F2:: {
    global
    isRunning := false
    loopActive := false
    SetTimer(PressE, 0)
    ToolTip("Script detenido. Cerrando...")
    Sleep(1000)
    ToolTip()
    ExitApp()
}

; Función que presiona la tecla 'e'
PressE() {
    global
    if (loopActive) {
        Send("e")
    }
}

; Mostrar instrucciones al iniciar
ToolTip("Script cargado. F1 = Iniciar/Pausar presionar 'e', F2 = Salir")
SetTimer(HideTooltip, 3000)

HideTooltip() {
    global
    ToolTip()
    SetTimer(HideTooltip, 0)
}

; ESC como tecla de emergencia para detener todo
Esc:: {
    global
    isRunning := false
    loopActive := false
    SetTimer(PressE, 0)
    ToolTip("DETENIDO POR ESC - Script pausado")
}
