<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flores Amarillas 🌻</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #87CEEB 0%, #98FB98 50%, #F0E68C 100%);
            min-height: 100vh;
            font-family: 'Georgia', serif;
            overflow-x: hidden;
            position: relative;
        }

        .container {
            position: relative;
            width: 100%;
            min-height: 100vh;
            padding: 20px;
        }

        /* Flores */
        .flower {
            position: absolute;
            animation: sway 3s ease-in-out infinite;
        }

        .stem {
            width: 4px;
            height: 80px;
            background: linear-gradient(to bottom, #228B22, #32CD32);
            border-radius: 2px;
            position: relative;
            margin: 0 auto;
        }

        .flower-head {
            width: 40px;
            height: 40px;
            background: radial-gradient(circle, #FFD700, #FFA500);
            border-radius: 50%;
            position: relative;
            margin: 0 auto;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
        }

        .petal {
            position: absolute;
            width: 20px;
            height: 30px;
            background: linear-gradient(45deg, #FFD700, #FFFF00);
            border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
            transform-origin: center bottom;
        }

        .petal:nth-child(1) { transform: rotate(0deg) translateY(-15px); }
        .petal:nth-child(2) { transform: rotate(45deg) translateY(-15px); }
        .petal:nth-child(3) { transform: rotate(90deg) translateY(-15px); }
        .petal:nth-child(4) { transform: rotate(135deg) translateY(-15px); }
        .petal:nth-child(5) { transform: rotate(180deg) translateY(-15px); }
        .petal:nth-child(6) { transform: rotate(225deg) translateY(-15px); }
        .petal:nth-child(7) { transform: rotate(270deg) translateY(-15px); }
        .petal:nth-child(8) { transform: rotate(315deg) translateY(-15px); }

        .center {
            position: absolute;
            width: 15px;
            height: 15px;
            background: radial-gradient(circle, #8B4513, #D2691E);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        /* Hojas */
        .leaf {
            position: absolute;
            width: 15px;
            height: 25px;
            background: linear-gradient(45deg, #228B22, #32CD32);
            border-radius: 0 100% 0 100%;
            top: 30px;
        }

        .leaf.left {
            left: -8px;
            transform: rotate(-30deg);
        }

        .leaf.right {
            right: -8px;
            transform: rotate(30deg);
        }

        /* Mensajes */
        .message {
            position: absolute;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px 20px;
            border-radius: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            font-size: 16px;
            color: #2F4F4F;
            text-align: center;
            max-width: 280px;
            animation: float 4s ease-in-out infinite;
            border: 2px solid #FFD700;
        }

        /* Animaciones */
        @keyframes sway {
            0%, 100% { transform: rotate(-2deg); }
            50% { transform: rotate(2deg); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @keyframes sparkle {
            0%, 100% { opacity: 0.7; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.1); }
        }

        /* Partículas brillantes */
        .sparkle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #FFD700;
            border-radius: 50%;
            animation: sparkle 2s ease-in-out infinite;
        }

        /* Título principal */
        .main-title {
            position: absolute;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 28px;
            color: #FF6347;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            text-align: center;
            background: rgba(255, 255, 255, 0.8);
            padding: 20px;
            border-radius: 20px;
            border: 3px solid #FFD700;
            animation: float 3s ease-in-out infinite;
        }

        /* Responsive para Samsung A51 */
        @media screen and (max-width: 412px) {
            .main-title {
                font-size: 24px;
                padding: 15px;
                top: 30px;
            }
            
            .message {
                font-size: 14px;
                padding: 12px 16px;
                max-width: 250px;
            }
            
            .flower-head {
                width: 35px;
                height: 35px;
            }
            
            .stem {
                height: 70px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="main-title">🌻 Jardín de Alegría 🌻</h1>

        <!-- Flores distribuidas por la pantalla -->
        <div class="flower" style="top: 150px; left: 50px;">
            <div class="flower-head">
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="center"></div>
            </div>
            <div class="stem">
                <div class="leaf left"></div>
                <div class="leaf right"></div>
            </div>
        </div>

        <div class="flower" style="top: 200px; right: 60px; animation-delay: -1s;">
            <div class="flower-head">
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="center"></div>
            </div>
            <div class="stem">
                <div class="leaf left"></div>
                <div class="leaf right"></div>
            </div>
        </div>

        <div class="flower" style="top: 350px; left: 30px; animation-delay: -2s;">
            <div class="flower-head">
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="center"></div>
            </div>
            <div class="stem">
                <div class="leaf left"></div>
                <div class="leaf right"></div>
            </div>
        </div>

        <div class="flower" style="top: 400px; right: 40px; animation-delay: -0.5s;">
            <div class="flower-head">
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="center"></div>
            </div>
            <div class="stem">
                <div class="leaf left"></div>
                <div class="leaf right"></div>
            </div>
        </div>

        <div class="flower" style="top: 550px; left: 70px; animation-delay: -1.5s;">
            <div class="flower-head">
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="center"></div>
            </div>
            <div class="stem">
                <div class="leaf left"></div>
                <div class="leaf right"></div>
            </div>
        </div>

        <div class="flower" style="top: 600px; right: 80px; animation-delay: -2.5s;">
            <div class="flower-head">
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="petal"></div>
                <div class="center"></div>
            </div>
            <div class="stem">
                <div class="leaf left"></div>
                <div class="leaf right"></div>
            </div>
        </div>

        <!-- Mensajes bonitos -->
        <div class="message" style="top: 180px; right: 20px; animation-delay: -1s;">
            "La vida es como una flor, hermosa y frágil" 🌼
        </div>

        <div class="message" style="top: 320px; left: 80px; animation-delay: -2s;">
            "Cada día es una nueva oportunidad para florecer" 🌻
        </div>

        <div class="message" style="top: 480px; right: 30px; animation-delay: -0.5s;">
            "Sonríe, eres más hermosa que cualquier flor" 😊
        </div>

        <div class="message" style="top: 650px; left: 20px; animation-delay: -1.5s;">
            "Las flores más bellas crecen después de la lluvia" 🌈
        </div>

        <!-- Partículas brillantes -->
        <div class="sparkle" style="top: 200px; left: 100px; animation-delay: -0.5s;"></div>
        <div class="sparkle" style="top: 300px; right: 120px; animation-delay: -1s;"></div>
        <div class="sparkle" style="top: 450px; left: 150px; animation-delay: -1.5s;"></div>
        <div class="sparkle" style="top: 580px; right: 90px; animation-delay: -2s;"></div>
        <div class="sparkle" style="top: 380px; left: 200px; animation-delay: -0.8s;"></div>
    </div>
</body>
</html>
