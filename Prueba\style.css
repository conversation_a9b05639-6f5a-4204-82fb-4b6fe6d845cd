@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,500;1,900&display=swap');

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Poppins', cursive;
}

/* Fondo atardecer */
body {
  width: 100%;
  height: 100vh;
  background: linear-gradient(to top, #ff914d, #ff5e78, #6d3b8f);
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  flex-direction: column;
  color: #fff3e0;
}

/* ---------- Texto animado ---------- */
.greetings {
  font-size: 6rem;
  font-weight: 900;
  text-align: center;
  margin-bottom: 1rem;
}

.greetings > span {
  animation: glow 2.5s ease-in-out infinite;
}

@keyframes glow {
  0%, 100% {
    color: #fff3e0;
    text-shadow: 0 0 12px #ffd166, 0 0 50px #ffb347, 0 0 100px #ff914d;
  }
  10%, 90% {
    color: #333;
    text-shadow: none;
  }
}

.greetings > span:nth-child(2) { animation-delay: .2s; }
.greetings > span:nth-child(3) { animation-delay: .4s; }
.greetings > span:nth-child(4) { animation-delay: .6s; }
.greetings > span:nth-child(5) { animation-delay: .8s; }
.greetings > span:nth-child(6) { animation-delay: 1s; }

/* Descripción */
.description {
  font-size: 1.5rem;
  margin-bottom: 20px;
  text-align: center;
}

/* ---------- Botones ---------- */
.botones a {
  display: inline-block;
  background-color: #ffb347;
  padding: 10px 20px;
  border-radius: 40px;
  text-decoration: none;
  font-size: 1rem;
  color: #222;
  transition: 0.3s;
}

.botones a:hover {
  background-color: #ffd166;
  color: #111;
}

/* ---------- Flores amarillas ---------- */
.flowers {
  position: absolute;
  bottom: 0;
  display: flex;
  gap: 3vmin;
  flex-wrap: wrap;
  justify-content: center;
  width: 100%;
}

.flower {
  position: relative;
  width: 8vmin;
  height: 12vmin;
  display: flex;
  justify-content: center;
  align-items: flex-end;
}

/* Tallo */
.stem {
  width: 0.6vmin;
  height: 10vmin;
  background: #2d7a3e;
  border-radius: 1vmin;
}

/* Pétalos */
.petals {
  position: absolute;
  bottom: 8vmin;
  width: 6vmin;
  height: 6vmin;
  background: #ffd166;
  border-radius: 50%;
  animation: bloom 3s ease-in-out infinite alternate;
  box-shadow: 0 0 1vmin #ff914d;
}

@keyframes bloom {
  0% { transform: scale(0.8) rotate(0deg); }
  100% { transform: scale(1.1) rotate(10deg); }
}

/* ---------- Luces parpadeantes ---------- */
.light {
  position: absolute;
  border-radius: 50%;
  background: #ffb347;
  width: 1vmin;
  height: 1vmin;
  animation: flicker 3s infinite ease-in-out;
  opacity: 0.8;
}

@keyframes flicker {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.5); opacity: 0.3; }
}

/* Posiciones de luces */
.light:nth-child(1) { top: 10%; left: 20%; }
.light:nth-child(2) { top: 30%; right: 15%; animation-delay: 1s; }
.light:nth-child(3) { bottom: 20%; left: 25%; animation-delay: 2s; }
.light:nth-child(4) { bottom: 30%; right: 20%; animation-delay: 1.5s; }

/* ---------- Responsivo ---------- */
@media screen and (max-width: 574px) {
  .greetings { font-size: 3rem; }
  .description { font-size: 1.2rem; }
}
