// Sincronizar las letras con la canción
var audio = document.querySelector("audio");
var lyrics = document.querySelector("#lyrics");

// Array de objetos que contiene cada línea y su tiempo de aparición en segundos
var lyricsData = [
  { text: "He's got the fire", time: 0 },
  { text: "And he walks with it", time: 3 },
  { text: "He's got the fire", time: 7 },
  { text: "And he talks with it", time: 11 },
  { text: "His Bonnie on the side, Bonnie on the side", time: 14 },
  { text: "Makes me a sad, sad girl", time: 19 },
  { text: "His money on the side, money on the side", time: 22 },
  { text: "Makes me a sad, sad girl", time: 27 },
  { text: "I'm a sad girl, I'm a sad girl", time: 30 },
  { text: "I'm a sad girl", time: 35 },
  { text: "I'm a sad girl, I'm a bad girl", time: 40 },
  { text: "I'm a bad girl", time: 45 },
  { text: "Being a bad bitch on the side", time: 50 },
  { text: "Might not appeal to fools like you", time: 56 },
  { text: "Creeping around while he gets high", time: 65},
  { text: "It might not be something you would do", time: 72 },
  { text: "But you haven't seen my man (man)", time: 76},
  { text: "You haven't seen my man (man, man)", time: 81 },
  { text: "You haven't seen my man (man)", time: 86 },
  { text: "You haven't seen him", time: 90 },
  { text: "He's got the fire", time: 93 },
  { text: "And he walks with it", time: 99 },
   { text: "He's got the fire", time: 102 },
  { text: "And he talks with it", time: 106 },
  { text: "His Bonnie on the side, Bonnie on the side", time: 110 },
  { text: "Makes me a sad, sad girl", time: 114 },
  { text: "His money on the side, money on the side", time: 118 },
  { text: "Makes me a sad, sad girl", time: 123 },
  { text: "I'm a sad girl, I'm a sad girl.", time: 127 },
];

// Animar las letras
function updateLyrics() {
  var time = Math.floor(audio.currentTime);
  var currentLine = lyricsData.find(
    (line) => time >= line.time && time < line.time + 6
  );

  if (currentLine) {
    // Calcula la opacidad basada en el tiempo en la línea actual
    var fadeInDuration = 0.1; // Duración del efecto de aparición en segundos
    var opacity = Math.min(1, (time - currentLine.time) / fadeInDuration);

    // Aplica el efecto de aparición
    lyrics.style.opacity = opacity;
    lyrics.innerHTML = currentLine.text;
  } else {
    // Restablece la opacidad y el contenido si no hay una línea actual
    lyrics.style.opacity = 0;
    lyrics.innerHTML = "";
  }
}

setInterval(updateLyrics, 1000);

//funcion titulo
// Función para ocultar el título después de 216 segundos
function ocultarTitulo() {
  var titulo = document.querySelector(".titulo");
  titulo.style.animation =
    "fadeOut 3s ease-in-out forwards"; /* Duración y función de temporización de la desaparición */
  setTimeout(function () {
    titulo.style.display = "none";
  }, 3000); // Espera 3 segundos antes de ocultar completamente
}

// Llama a la función después de 216 segundos (216,000 milisegundos)
setTimeout(ocultarTitulo, 216000);