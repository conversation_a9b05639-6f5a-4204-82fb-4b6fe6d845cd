#!/usr/bin/env python3
"""
Script de Python para presionar la tecla 'e' infinitamente
F1: Iniciar/pausar el presionado de 'e'
F2: Detener y cerrar el script
ESC: Pausa de emergencia

Optimizado para bajo consumo de recursos
"""

import time
import threading
import sys
import os
from pynput import keyboard
from pynput.keyboard import Key, Listener
import pynput.keyboard as kb

class AutoEPresser:
    def __init__(self):
        self.is_running = False
        self.loop_active = False
        self.controller = kb.Controller()
        self.press_thread = None
        self.listener = None
        self.should_exit = False
        
        # Configuración optimizada para rendimiento
        self.press_interval = 0.1  # 100ms entre pulsaciones (10 por segundo)
        self.sleep_interval = 0.01  # 10ms de sleep en el bucle principal
        
        print("🎮 Auto E Presser iniciado")
        print("📋 Controles:")
        print("   F1  = Iniciar/Pausar presionar 'e'")
        print("   F2  = Cerrar programa")
        print("   ESC = Pausa de emergencia")
        print("💡 Presiona F1 para comenzar...")

    def press_e_loop(self):
        """Bucle optimizado para presionar 'e' con bajo consumo de CPU"""
        last_press_time = 0
        
        while self.loop_active and not self.should_exit:
            current_time = time.time()
            
            # Solo presiona si ha pasado el intervalo necesario
            if current_time - last_press_time >= self.press_interval:
                if self.is_running and self.loop_active:
                    try:
                        self.controller.press('e')
                        self.controller.release('e')
                        last_press_time = current_time
                    except Exception as e:
                        print(f"❌ Error al presionar tecla: {e}")
                        break
            
            # Sleep corto para reducir uso de CPU
            time.sleep(self.sleep_interval)

    def start_pressing(self):
        """Inicia el presionado de 'e'"""
        if not self.is_running:
            self.is_running = True
            self.loop_active = True
            
            # Crear hilo para presionar teclas (no bloquea el programa principal)
            self.press_thread = threading.Thread(target=self.press_e_loop, daemon=True)
            self.press_thread.start()
            
            print("✅ Presionando 'e' cada 100ms... (F1 para pausar)")
        else:
            self.pause_pressing()

    def pause_pressing(self):
        """Pausa el presionado de 'e'"""
        self.is_running = False
        self.loop_active = False
        print("⏸️  Pausado. Presiona F1 para reanudar o F2 para salir")

    def stop_and_exit(self):
        """Detiene todo y cierra el programa"""
        print("🛑 Deteniendo script...")
        self.is_running = False
        self.loop_active = False
        self.should_exit = True
        
        # Esperar a que termine el hilo de presionado
        if self.press_thread and self.press_thread.is_alive():
            self.press_thread.join(timeout=1.0)
        
        # Detener el listener de teclado
        if self.listener:
            self.listener.stop()
        
        print("👋 Script cerrado. ¡Hasta luego!")
        sys.exit(0)

    def on_key_press(self, key):
        """Maneja las teclas presionadas"""
        try:
            # F1 - Iniciar/Pausar
            if key == Key.f1:
                self.start_pressing()
            
            # F2 - Salir
            elif key == Key.f2:
                self.stop_and_exit()
            
            # ESC - Pausa de emergencia
            elif key == Key.esc:
                if self.is_running:
                    self.pause_pressing()
                    print("🚨 PAUSA DE EMERGENCIA - Presiona F1 para reanudar")
                
        except AttributeError:
            # Ignorar teclas especiales que no se pueden procesar
            pass
        except Exception as e:
            print(f"❌ Error procesando tecla: {e}")

    def run(self):
        """Ejecuta el programa principal"""
        try:
            # Configurar listener de teclado global
            self.listener = Listener(on_press=self.on_key_press)
            self.listener.start()
            
            # Mantener el programa corriendo
            while not self.should_exit:
                time.sleep(0.1)  # Sleep para reducir uso de CPU
                
        except KeyboardInterrupt:
            print("\n🛑 Interrupción por teclado detectada")
            self.stop_and_exit()
        except Exception as e:
            print(f"❌ Error inesperado: {e}")
            self.stop_and_exit()

def main():
    """Función principal"""
    try:
        # Verificar que las dependencias estén disponibles
        import pynput
        
        # Crear y ejecutar el auto presser
        auto_presser = AutoEPresser()
        auto_presser.run()
        
    except ImportError:
        print("❌ Error: La librería 'pynput' no está instalada")
        print("💡 Instálala con: pip install pynput")
        input("Presiona Enter para salir...")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error fatal: {e}")
        input("Presiona Enter para salir...")
        sys.exit(1)

if __name__ == "__main__":
    main()
